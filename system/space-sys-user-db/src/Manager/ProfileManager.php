<?php
namespace App\Manager;

use Exception;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\ProfileService;
use Symfony\Component\HttpFoundation\Response;
use App\Trait\LoggerTrait;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Manager of the user Account
 */
class ProfileManager
{
    use LoggerTrait;

    const COLLECTION = 'userData';

    /**
     * @param ValidatorInterface $validator
     * @param ProfileService $profileService
     */
    public function __construct(
        private ValidatorInterface $validator,
        private ProfileService $profileService
    ) {
    }

    /**
     * Description: update profile user account
     * @param string $id
     * @param array $data
     */
    public function putUserData(string $id, ?array $data)
    {
        try {
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . " Put User data");

            $response = [];
            $hasUserDataFields = false;
            $hasAddressFields = false;
            $hasContactFields = false;

            // Check if user data fields are present
            if (
                isset($data['civility']) || isset($data['firstName']) || isset($data['lastName']) || isset($data['email'])
            ) {
                $hasUserDataFields = true;
            }

            // Check if address fields are present
            if (
                isset($data['street']) || isset($data['town']) ||
                isset($data['postCode']) || isset($data['addressLine2'])
            ) {
                $hasAddressFields = true;
            }

            // Check if contact fields are present
            if (isset($data['phoneNumber'])) {
                $hasContactFields = true;
            }

            $userDataSuccess = false;
            $addressSuccess = false;
            $contactSuccess = false;
            $errors = [];

            // Call putUserData only if relevant fields are present
            if ($hasUserDataFields) {
                $userDataResponse = $this->profileService->putUserData($id, $data);
                if (Response::HTTP_OK == $userDataResponse->getCode()) {
                    $userDataSuccess = true;
                    $this->logger->info('Successfully updated user data customerId => ' . $id);
                } else {
                    $responseData = $userDataResponse->getData();
                    $message = $responseData['message'] ?? $responseData;
                    $errors[] = 'User data update failed: ' . $message;
                    $this->logger->error('Failed to update user data', [
                        'customerId' => $id,
                        'errorCode' => $userDataResponse->getCode(),
                        'errorMessage' => $message
                    ]);
                    // Continue to try address and contact updates even if user data fails
                }
            }

            // Call putUserAddress regardless of putUserData success/failure if address fields are present
            if ($hasAddressFields) {
                $data["addressTypeId"] = 2;
                $responseAddress = $this->profileService->putUserAddress($id, $data);
                if (Response::HTTP_OK == $responseAddress->getCode()) {
                    $addressSuccess = true;
                    $this->logger->info('Successfully updated user address details customerId => ' . $id);
                } elseif (Response::HTTP_NOT_FOUND == $responseAddress->getCode()) {
                    $this->logger->info('Failed to update user address customerId => ' . $id);
                    $data["addressTypeName"] = "mailing";
                    $addAddressResponse = $this->profileService->addUserAddress($id, $data);
                    if (Response::HTTP_CREATED == $addAddressResponse->getCode()) {
                        $addressSuccess = true;
                        $this->logger->info('Successfully added user address details customerId => ' . $id);
                    } else {
                        $responseData = $addAddressResponse->getData();
                        $message = $responseData['message'] ?? $responseData;
                        $errors[] = 'Address add failed: ' . $message;
                        $this->logger->error('Failed to add user address', [
                            'customerId' => $id,
                            'errorCode' => $addAddressResponse->getCode(),
                            'errorMessage' => $message
                        ]);
                    }
                } else {
                    $responseData = $responseAddress->getData();
                    $message = $responseData['message'] ?? $responseData;
                    $errors[] = 'Address update failed: ' . $message;
                    $this->logger->error('Failed to update user address', [
                        'customerId' => $id,
                        'errorCode' => $responseAddress->getCode(),
                        'errorMessage' => $message
                    ]);
                }
            }

            // Call putUserContact regardless of putUserData success/failure if contact fields are present
            if ($hasContactFields) {
                $data["contactTypeId"] = 1;
                if (isset($data['countryCode'])) {
                    $data["contactCountryCode"] = $data["countryCode"];
                }
                $responseContact = $this->profileService->putUserContact($id, $data);
                if (Response::HTTP_OK == $responseContact->getCode()) {
                    $contactSuccess = true;
                    $this->logger->info('Successfully updated user contact details customerId => ' . $id);
                } elseif (Response::HTTP_NOT_FOUND == $responseContact->getCode()) {
                    $this->logger->info('Failed to update user contact customerId => ' . $id);
                    $data['contactTypeName'] = "primary";
                    $addContactResponse = $this->profileService->addUserContact($id, $data);
                    if (Response::HTTP_CREATED == $addContactResponse->getCode()) {
                        $contactSuccess = true;
                        $this->logger->info('Successfully added user contact details customerId => ' . $id);
                    } else {
                        $responseData = $addContactResponse->getData();
                        $message = $responseData['message'] ?? $responseData;
                        $errors[] = 'Contact add failed: ' . $message;
                        $this->logger->error('Failed to add user contact', [
                            'customerId' => $id,
                            'errorCode' => $addContactResponse->getCode(),
                            'errorMessage' => $message
                        ]);
                    }
                } else {
                    $responseData = $responseContact->getData();
                    $message = $responseData['message'] ?? $responseData;
                    $errors[] = 'Contact update failed: ' . $message;
                    $this->logger->error('Failed to update user contact', [
                        'customerId' => $id,
                        'errorCode' => $responseContact->getCode(),
                        'errorMessage' => $message
                    ]);
                }
            }

            // Check if any operations failed
            $anyOperationAttempted = $hasUserDataFields || $hasAddressFields || $hasContactFields;
            $anyOperationFailed = ($hasUserDataFields && !$userDataSuccess) ||
                ($hasAddressFields && !$addressSuccess) ||
                ($hasContactFields && !$contactSuccess);

            // If any operation failed, return error response
            if ($anyOperationFailed) {
                $errorMessage = implode('; ', $errors);
                return new ErrorResponse($errorMessage, Response::HTTP_BAD_REQUEST);
            }

            // If at least one operation was attempted and all succeeded
            if ($anyOperationAttempted) {
                return new SuccessResponse("Successfully updated");
            }

            // If no fields were provided
            return new ErrorResponse('No valid fields provided for update', Response::HTTP_BAD_REQUEST);

        } catch (Exception $e) {
            $this->logger->error("Error: Put Profile data for $id " . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Description: update profile user account
     * @param string $id
     */
    public function getUserData(string $id)
    {
        try {
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . " get User data");

            $response = $this->profileService->getUserData($id);
            if (Response::HTTP_OK == $response->getCode()) {
                $response = ($response->getData())["data"];
                // Get primary phone number from contacts
                if (isset($response["contacts"]) && sizeof($response["contacts"]) > 0) {
                    $primaryContact = null;
                    foreach ($response["contacts"] as $contact) {
                        if (isset($contact["contactTypeName"]) && $contact["contactTypeName"] === "primary") {
                            $primaryContact = $contact;
                            break;
                        }
                    }
                    if ($primaryContact) {
                        $response["phoneNumber"] = $primaryContact["phoneNumber"];
                    }
                }

                // Get mailing address details
                if (isset($response["addresses"]) && sizeof($response["addresses"]) > 0) {
                    $mailingAddress = null;
                    foreach ($response["addresses"] as $address) {
                        if (isset($address["addressTypeName"]) && $address["addressTypeName"] === "mailing") {
                            $mailingAddress = $address;
                            break;
                        }
                    }
                    if ($mailingAddress) {
                        $response["town"] = $mailingAddress["town"];
                        $response["countryCode"] = $mailingAddress["countryCode"];
                        $response["postCode"] = $mailingAddress["postCode"];
                        $response["street"] = $mailingAddress["street"];
                        $response["addressLine2"] = $mailingAddress["addressLine2"];
                    }
                }
                return new SuccessResponse($response ?? []);
            }
            $responseData = $response->getData();
            $message = $responseData['message'] ?? $responseData;
            $this->logger->error('Failed to update user ', [
                'id' => $id,
                'errorCode' => $response->getCode(),
                'errorMessage' => $message
            ]);
            return new ErrorResponse($message, $response->getCode());
        } catch (Exception $e) {
            $this->logger->error("Error: Put Profile data for $id " . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}