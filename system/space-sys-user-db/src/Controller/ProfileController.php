<?php

namespace App\Controller;

use App\Manager\ProfileManager;
use App\Model\ProfileModel;
use App\Trait\ValidationResponseTrait;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Nelmio\ApiDocBundle\Annotation\Model;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('v1', name: 'Profile')]
class ProfileController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/user/{id}', name: '_update', methods: ['PUT'])]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Customer Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\RequestBody(
        content: new JsonContent(ref: new Model(type: ProfileModel::class))
    )]
    #[OA\Tag(name: 'Profile')]
    #[OA\Response(
        response: 200,
        description: 'Ok'
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function updateProfileInfo(string $id,ValidatorInterface $validator, ProfileManager $profileManager, Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $response = $profileManager->putUserData(
            $id,
            $data
        )->toArray();

        if ($response['code'] == 200) {
            return $this->json(["success" => "successfully updated"], $response['code']);
        }
        return $this->json($response['content'], $response['code']);
    }

    #[Route('/user/{id}', name: '_get', methods: ['GET'])]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Customer Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Tag(name: 'Profile')]
    #[OA\Response(
        response: 200,
        description: 'Ok'
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getProfileInfo(string $id,ValidatorInterface $validator, SerializerInterface $serializer, ProfileManager $profileManager, Request $request): JsonResponse
    {
        $response = $profileManager->getUserData(
            $id,
        )->toArray();

        return $this->json($response['content'], $response['code']);
    }
}