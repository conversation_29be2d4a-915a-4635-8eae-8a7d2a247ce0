<?php

namespace App\Controller;

use Symfony\Component\Validator\ConstraintViolationListInterface;
use App\Manager\ProfileManager;
use App\Model\ProfileModel;
use App\Trait\ValidationResponseTrait;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Nelmio\ApiDocBundle\Annotation\Model;
use Symfony\Component\Serializer\SerializerInterface;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use App\Validator\BrandValidator;


#[Route('v1', name: 'profile')]
class ProfileController extends AbstractController
{
    use ValidationResponseTrait;

    public function __construct(
        private ValidatorInterface $validator
    ) {}

    #[Route('/profile', name: '_update', methods: ['PUT'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        description: 'Language',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'civility', type: 'string', description: 'User civility'),
                new OA\Property(property: 'civilityCode', type: 'string', description: 'Civility code'),
                new OA\Property(property: 'last_name', type: 'string', description: 'Last name'),
                new OA\Property(property: 'first_name', type: 'string', description: 'First name'),
                new OA\Property(property: 'address1', type: 'string', description: 'Address line 1'),
                new OA\Property(property: 'address2', type: 'string', description: 'Address line 2'),
                new OA\Property(property: 'city', type: 'string', description: 'City'),
                new OA\Property(property: 'zip_code', type: 'string', description: 'Zip code'),
                new OA\Property(property: 'phone', type: 'string', description: 'Phone number')
            ]
        )
    )]
    #[OA\Tag(name: 'Profile')]
    #[OA\Response(
        response: 200,
        description: 'Ok',
        content: new Model(type: ProfileModel::class)
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function updateProfileInfo(ValidatorInterface $validator, ProfileManager $profileManager, Request $request, SerializerInterface $serializer): JsonResponse
    {
        $country = $request->query->get('country');
        $language = $request->query->get('language');
        $params = [
            'country' => $country,
        ];
        $listConstraints = [
            'country' => CountryValidator::getConstraints()
        ];

        $errors = $this->validateConstraint(
            $listConstraints,
            $params
        );

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }

        $userId = $request->headers->get('userId');
        $data = json_decode($request->getContent(), true);

        $data['country'] = $country;
        $response = $profileManager->putUserData(
            $userId,
            $data
        )->toArray();

        if ($response['code'] == 200) {
            return $this->json(["success" => "Successfully Updated"], $response['code']);
        }
        return $this->json($response['content'], $response['code']);
    }

    #[Route('/profile', name: '_get', methods: ['GET'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Tag(name: 'Profile')]
    #[OA\Response(
        response: 200,
        description: 'Ok',
        content: new Model(type: ProfileModel::class)
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Forbidden',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getProfileInfo(ValidatorInterface $validator, ProfileManager $profileManager, Request $request): JsonResponse
    {
        $userId = $request->headers->get('userId');

        $response = $profileManager->getUserData(
            $userId
        )->toArray();

        return $this->json($response['content'], $response['code']);
    }

    /**
     * @param ValidatorInterface $validator
     * @param string $brand
     * @param string $country
     * @param string $language
     * @return array
     */
    private function validate(ValidatorInterface $validator,string  $brand, string $country, string $language = null): array
    {
        $inputs = ['brand' => $brand, 'country' => $country];
        $constraints =  [
            'brand'   => BrandValidator::getConstraints(),
            'country' => CountryValidator::getConstraints()
        ];

        if ($language) {
            $inputs['language'] = $language;
            $constraints['language'] = LanguageValidator::getConstraintsForLanguage();
        }

        $errors = $validator->validate(
            $inputs,
            new Assert\Collection($constraints)
        );

        return static::getValidationMessages($errors);
    }

    private function validateConstraint(
        array $listConstraints,
        array $params
    ): ConstraintViolationListInterface {
        return $this->validator->validate(
            $params,
            new Assert\Collection($listConstraints)
        );
    }
}