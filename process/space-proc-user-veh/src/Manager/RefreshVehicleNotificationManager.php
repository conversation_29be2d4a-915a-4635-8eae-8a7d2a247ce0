<?php

namespace App\Manager;

use App\Helper\LcdvsProvider;
use App\Helper\RefreshVehicleHelper;
use App\Helper\SuccessResponse;
use App\Message\ObjectSubsciption;
use App\Message\RefreshVehicleSubscriptionMessage;
use App\Message\Subscription;
use App\Model\RefreshVehicleNotificationRequest;
use App\Service\CorvetService;
use App\Service\MongoAtlasQueryService;
use App\Service\SystemUserDataClient;
use App\Trait\LoggerTrait;
use App\Trait\ValidationResponseTrait;
use App\Validator\BrandValidator;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use App\Validator\VinValidator;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Refresh Vehicle Notification Manager.
 */
class RefreshVehicleNotificationManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    public function __construct(
        private ValidatorInterface $validator,
        private CorvetService $corvetService,
        private MongoAtlasQueryService $mongoAtlasQueryService,
        private SystemUserDataClient $systemUserDataClient,
        private MessageBusInterface $bus
    ) {
    }
    public function handleSqsMessages(
        RefreshVehicleNotificationRequest $notification
    ) {
        $vin = $notification->getObject()->getAddVehicleModel()->getVin();
        $brand = $notification->getObject()->getAddVehicleModel()->getBrand();
        $country = $notification->getObject()->getAddVehicleModel()->getCountry();
        $language = $notification->getObject()->getAddVehicleModel()->getLanguage();
        $source = $notification->getObject()->getAddVehicleModel()->getSource();
        $mileage = $notification->getObject()->getAddVehicleModel()->getMileage();
        $userId = $notification->getUserId();
        // doing validation
        $messages = $this->getMessagesForValidation(
            $vin,
            $brand,
            $country,
            $language,
            $userId
        );
        if (!empty($messages)) {
            $this->logger->error('RefreshVehicleNotificationManager', ['message' => $messages]);
            return $this->getValidationErrorResponse($messages);
        }

        //call corvet to retrive lcdv
        $lcdv = $this->getLcdv($vin, $brand);
        if (!$lcdv) {
            $this->logger->error('RefreshVehicleNotificationManager: Error lcdv not found while calling corvet');
            return;
        }
        //call mongoDb vehicleLabel to get label
        $label = $this->getLabel($lcdv);
        if (!$label) {
            $this->logger->error('RefreshVehicleNotificationManager: Error label not found while calling mongoDb vehicleLabel');
            return;
        }
        //call mongodb to get psaId
        $psaId = $this->getPsaId($userId, $brand);
        if (!$psaId) {
            $this->logger->error('RefreshVehicleNotificationManager: Error psaId not found while calling mongoDb userData');
            return;
        }
        // no need for MMW
        $siteCode = RefreshVehicleHelper::getSiteCode($brand, $country, 'APP');
        //call C@ to get ticket
        $ticket = $this->getTicket($psaId, $siteCode);
        if (!$ticket) {
            $this->logger->error('RefreshVehicleNotificationManager: Error Ticket not found while calling C@');
            return;
        }
        // add vehicle to customer@
        $response = $this->addVehicle(
            $vin,
            $label,
            $lcdv,
            $ticket,
            $siteCode,
            $language
        );
        if (!$response) {
            $this->logger->error('RefreshVehicleNotificationManager: Error while adding vehicle in C@ VIN : ' . $vin);
            return;
        }
        // calling mongoDb to update userData and set the mileage
        $this->updateVehicleData($userId, $vin, $label, $lcdv, $mileage);

        $refreshVehicleSubscriptionMessage = $this->buildMessage($vin, $userId, $source);

        $this->pushNotificationSQS($refreshVehicleSubscriptionMessage);

        return new SuccessResponse('Vehicle added successfully', Response::HTTP_OK);
    }

    private function findInMongoBy(
        string $collection,
        array $filters
    ): ?array {
        $response = $this->mongoAtlasQueryService->find(
            $collection,
            $filters
        );

        return json_decode($response->getData(), true)['documents'][0] ?? [];
    }

    private function pushNotificationSQS(
        RefreshVehicleSubscriptionMessage $message
    ) {
        try {
            $this->bus->dispatch($message);
            $this->logger->info("Pushing message to sqs class: " . get_class($this));
        } catch (\Exception $e) {
            $this->logger->error('ERROR occured while dispatching settings message in sqs class : ' . get_class($this) . " message: " . $e->getMessage());
        }
    }

    /**
     * @param string $vin
     * @param string $userId
     * @param string $source
     * 
     * @return RefreshVehicleSubscriptionMessage
     */
    private function buildMessage(
        string $vin,
        string $userId,
        string $source
    ): RefreshVehicleSubscriptionMessage {
        $refreshVehicleSubscriptionMessage = new RefreshVehicleSubscriptionMessage();
        $subscription = new Subscription();
        $subscription->setVin($vin);
        $objectSubscription = new ObjectSubsciption();
        $objectSubscription->setSubscription($subscription);
        $refreshVehicleSubscriptionMessage->setMessageId(RefreshVehicleHelper::generateUid());
        $refreshVehicleSubscriptionMessage->setUserId($userId);
        $refreshVehicleSubscriptionMessage->setObjects($objectSubscription);
        $refreshVehicleSubscriptionMessage->setSource($source);

        return $refreshVehicleSubscriptionMessage;
    }

    /**
     * @param string $userId
     * @param string $vin
     * @param string $label
     * @param string $lcdv
     * @param int $mileage
     */
    private function updateVehicleData(
        string $userId,
        string $vin,
        string $label,
        string $lcdv,
        int $mileage = 0
    ) {
        $updatedVehicle = [
            "vehicle.$._id" => RefreshVehicleHelper::generateUid(),
            "vehicle.$.versionId" => $lcdv,
            "vehicle.$.vin" => $vin,
            "vehicle.$.label" => $label,
            "vehicle.$.mileage.value" => $mileage,
            "vehicle.$.mileage.date" => time(),
            "vehicle.$.addStatus" => "COMPLETE"
        ];

        $this->mongoAtlasQueryService->updateOne(
            'userData',
            [
                'userId' => $userId,
                'vehicle.vin' => $vin
            ],
            $updatedVehicle
        );
    }

    /**
     * @param string $vin
     * @param string $brand
     * 
     * @return string|null
     */
    private function getLcdv(
        string $vin,
        string $brand
    ): ?string {
        $corvetData = $this->corvetService->getData($vin, $brand);
        return $corvetData['VEHICULE']['DONNEES_VEHICULE']['LCDV_BASE'] ?? null;
    }

    /**
     * @param string $lcdv
     * 
     * @return string|null
     */
    private function getLabel(string $lcdv): ?string
    {
        $vehicleLabel = $this->findInMongoBy(
            'vehicleLabel',
            [
                'lcdv' => [
                    '$in' => LcdvsProvider::getLcdvsFormatArray($lcdv)
                ]
            ]
        );

        return $vehicleLabel['label'] ?? null;
    }

    /**
     * @param string $userId
     * @param string $brand
     * 
     * @return string|null
     */
    private function getPsaId(
        string $userId,
        string $brand
    ): ?string {
        $userData = $this->findInMongoBy(
            'userData',
            [
                'userId' => $userId,
                'userPsaId.brand' => $brand
            ]
        );
        return RefreshVehicleHelper::parsePsaId($userData['userPsaId'][0]['cvsId'] ?? '');
    }


    /**
     * @param string $psaId
     * @param string $siteCode
     * 
     * @return string|null
     */
    private function getTicket(
        string $psaId,
        string $siteCode
    ): ?string {
        // get ticket from user data
        $userdata = $this->systemUserDataClient->getV1CatTicket($psaId, $siteCode);
        return $userdata->getData()['success']['ticket'] ?? null;
    }

    /**
     * @param string $vin
     * @param string $label
     * @param string $lcdv
     * @param string $ticket
     * @param string $siteCode
     * @param string $language
     * 
     * @return array|null
     */
    private function addVehicle(
        string $vin,
        string $label,
        string $lcdv,
        string $ticket,
        string $siteCode,
        string $language
    ): ?array {

        $data = [
            'VEH_VIN' => $vin,
            'VEH_CLASS_LABEL' => $label,
            'VEH_LCDV' => $lcdv,
            'VEH_VIS' => substr($vin, 9),
        ];
        // insert into- customer@
        $response = $this->systemUserDataClient->addVehicle($ticket, $siteCode, $language, $data);

        return $response->getData()['success'] ?? null;
    }

    /**
     * @param string|null $vin
     * @param string|null $brand
     * @param string|null $country
     * @param string|null $language
     * @param string|null $userId
     * 
     * @return array|null
     */
    private function getMessagesForValidation(
        ?string $vin,
        ?string $brand,
        ?string $country,
        ?string $language,
        ?string $userId
    ): ?array {
        $errors = $this->validator->validate(
            compact('vin', 'brand', 'country', 'language', 'userId'),
            new Assert\Collection([
                'vin' => VinValidator::getConstraints(),
                'brand' => BrandValidator::getConstraintsForAll(),
                'country' => CountryValidator::getConstraintsForCountry(),
                'language' => LanguageValidator::getConstraintsForLanguage(),
                'userId' => new Assert\NotBlank,
            ])
        );
        return $this->getValidationMessages($errors);
    }
}
