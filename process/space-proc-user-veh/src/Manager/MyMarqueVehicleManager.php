<?php

declare(strict_types=1);

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Model\MyMarqueVehicleModel;
use App\Service\ProcessMeV2ClientService;
use App\Service\VehicleService;
use App\Trait\LoggerTrait;
use App\Trait\ValidationResponseTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class MyMarqueVehicleManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    public const TRACKING_STATUS_DELIVERED_TO_CUSTOMER = 'DELIVERED_CUSTOMER';

    public function __construct(
        private ProcessMeV2ClientService $processMeV2ClientService,
        private ValidatorInterface $validator,
        private VehicleService $vehicleService
    ) {
    }

    public function addVehicleToUserGarage(MyMarqueVehicleModel $myMarqueVehicleModel): ResponseArrayFormat
    {
        try {
            $violations = $this->validator->validate($myMarqueVehicleModel, null, ['addingToGarage']);

            if (0 < count($violations)) {
                $violationsDescription = $this->getValidationMessages($violations);
                $this->logger->error(__METHOD__ . ' Validation fails for incoming request', [
                    'vin' => $myMarqueVehicleModel->getVin(),
                    'model' => $myMarqueVehicleModel,
                    'desc' => $violationsDescription,
                ]);

                return $this->getValidationErrorResponse($violationsDescription);
            }

            $response = $this->processMeV2ClientService->addVehicle(
                $myMarqueVehicleModel->getAcnt(),
                $myMarqueVehicleModel->getVin(),
                $myMarqueVehicleModel->getMileage(),
                $myMarqueVehicleModel->getBrand(),
                $myMarqueVehicleModel->getCountry(),
                $myMarqueVehicleModel->getLanguage(),
                $myMarqueVehicleModel->isCheckActivation(),
                $myMarqueVehicleModel->getSource()
            );

            if (! in_array($response->getCode(), [Response::HTTP_OK, Response::HTTP_CREATED])) {
                return new ErrorResponse($response->getData(), $response->getCode());
            } else {
                // save flag isOrder to false
                $this->vehicleService->updateVehicleIsOrderFlag(
                    $myMarqueVehicleModel->getAcnt(),
                    $myMarqueVehicleModel->getVin(),
                    false
                );
            }

            return new SuccessResponse($response->getData(), $response->getCode());
        } catch (\Throwable $e) {
            $this->logger->error(__METHOD__ .' Catching Exception '.$e->getMessage(), [
                'vin' => $myMarqueVehicleModel->getVin(),
                'model' => $myMarqueVehicleModel,
                'exception' => $e,
            ]);

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
