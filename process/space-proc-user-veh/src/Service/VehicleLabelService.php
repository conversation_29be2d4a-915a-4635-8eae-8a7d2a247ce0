<?php 

namespace App\Service;

use App\Helper\LcdvsProvider;
use App\Helper\WSResponse;
use App\MongoDB\UserData\UserDataDocument\Vehicle;
use App\MongoDB\VehicleLabel\VehicleLabelDocument;
use App\MongoDB\VehicleLabel\VehicleLabelDocumentCollection;
use App\Trait\LoggerTrait;
use Symfony\Component\Serializer\SerializerInterface;

class VehicleLabelService
{
    use LoggerTrait;

    public const COLLECTION = 'vehicleLabel';

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private SerializerInterface $serializer
    )
    {
    }

    public function getVehicleLabelDocumentByLcdv(string $lcdvCode): WSResponse
    {
        $response = $this->mongoService->aggregate(
            self::COLLECTION,
            [
                [
                    '$match' => [
                        'lcdv' => [
                            '$in' => LcdvsProvider::getLcdvsFormatArray($lcdvCode)
                        ]
                    ]
                ],
                //add a field named lcdvLenght dinamically calculated that contains the length of the lcdv
                [
                    '$addFields' => [
                        'lcdvLength' => [
                            '$cond' => [
                                'if' => ['$isArray' => '$lcdv'],
                                'then' => ['$max' => ['$map' => [
                                    'input' => '$lcdv',
                                    'as' => 'lcdvItem',
                                    'in' => ['$strLenCP' => '$$lcdvItem']
                                ]]],
                                'else' => ['$strLenCP' => '$lcdv']
                            ]
                        ]
                    ]
                ],
                // apply a descending sort on the lcdvLength field
                [
                    '$sort' => [
                        'lcdvLength' => -1
                    ]
                ],
                // remove the lcdvLength field from the final result
                [
                    '$project' => [
                        'lcdvLength' => 0
                    ]
                ],
                // limit the result to 1 document
                [
                    '$limit' => 1
                ]
            ]
        );   

        return $response;
    }

    public function findLabelByLongestMatchingLcdv(string $lcdvCode): ?string
    {
        $response = $this->getVehicleLabelDocumentByLcdv($lcdvCode);

        try {
            $documents = $this->serializer->deserialize($response->getData(), VehicleLabelDocumentCollection::class, 'json')->getDocuments();
        } catch (\Exception $e) {
            $this->logger->error('Error while deserializing vehicle label documents', ['exception' => $e]);
            return null;
        }
        
        $document = $documents[0] ?? null;
        return $document?->label;
    }
}