<?php

namespace App\Service;

use App\Connector\SystemOmniConnector;
use App\Helper\BrandHelper;
use App\Helper\WSResponse;
use App\Model\VehicleModel;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Uid\Uuid;

/**
 * Vehicle service.
 */
class VehicleService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';
    public const CREATION_STATUS = 'CREATION';

    private VehicleModel $vehicleModel;

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private SerializerInterface $serializer,
        private SystemOmniConnector $sysOmniConnector,
        private CorvetService $corvetService,
        private BrandHelper $brandHelper
    ) {
    }

    /**
     * Get all vehicles on order for a user.
     */
    public function getVehiclesOnOrder(string $userId): WSResponse
    {
        return $this->mongoService->aggregate(self::COLLECTION, $this->getVehicleOrderFilter($userId));
    }

    /**
     * Mark orders as seen by updating field isUpdated to false.
     */
    public function setOrderIsUpdated(string $userId, string $vehicleId, bool $isUpdated): WSResponse
    {
        return $this->mongoService->updateOne(
            self::COLLECTION,
            [
                'userId' => $userId,
                'vehicle.id' => $vehicleId,
            ],
            [
                'vehicle.$.vehicleOrder.isUpdated' => $isUpdated,
            ]
        );
    }

    /**
     * Build the pipepline to serve the order aggregate request.
     */
    public function getVehicleOrderFilter(string $userId, ?bool $isOrder = true): array
    {
        return
            [
                [
                    '$match' => ['userId' => $userId],
                ],
                [
                    '$unwind' => '$vehicle',
                ],
                [
                    '$match' => [
                        'vehicle.isOrder' => ['$eq' => $isOrder],
                    ],
                ],
                [
                    '$group' => [
                        '_id' => 'vehiclesOnOrder',
                        'vehicle' => [
                            '$push' => '$vehicle',
                        ],
                    ],
                ],
            ];
    }

    /**
     * create or update vehicle function.
     */
    public function createOrUpdateVehicle(string $userId, VehicleModel $vehicleModel): WSResponse
    {
        $dbUserDataId = $this->getUserData($userId);
        $this->vehicleModel = $vehicleModel;
        $this->vehicleModel->setIsOrder(true);
        if (
            'DELIVERED_CUSTOMER' == $vehicleModel->getVehicleOrder()->getTrackingStatus()
            && $this->brandHelper->isXpBrand($vehicleModel->getBrand())
        ) {
            $this->vehicleModel->setIsOrder(false);
            $this->vehicleModel->setAddStatus(self::CREATION_STATUS);
        }
        // if user exist ;
        if (! empty($dbUserDataId)) {
            // update vehicle order
            $vehicle = $this->getVehicleByMop($dbUserDataId);
            if (! empty($vehicle['documents'])) {
                // update vehicle object if we have mopId exist
                $this->vehicleModel->setId($vehicle['documents'][0]['vehicle'][0]['id']);
                return $this->updateVehicle($dbUserDataId);
            }
            // push new vehicle in vehicles object
            return $this->insertVehicle($dbUserDataId);
        }

        // create new user with vehicle object
        return $this->createUserWithVehicle($userId);
    }

    /**
     * create user data and vehicle object.
     */
    public function createUserWithVehicle(string $userId): WSResponse
    {
        $this->vehicleModel->setId($this->getNewUuid());
        $this->vehicleModel->getVehicleOrder()->setIsUpdated(true);
        $this->vehicleModel->setIsOrder(true);
        $fields = ['userId' => $userId, 'vehicle' => [$this->getVehicleArray()]];

        return $this->mongoService->insertOne(self::COLLECTION, $fields);
    }

    /**
     * find vehicle object by MOP.
     */
    public function getVehicleByMop(string $dbUserDataId)
    {
        $mopId = $this->vehicleModel->getVehicleOrder()->getMopId();
        $pipeline = [
            ['$match' => ['_id' => ['$oid' => $dbUserDataId]]],
            ['$unwind' => '$vehicle'],
            ['$match' => ['vehicle.vehicleOrder.mopId' => ['$eq' => $mopId]]], //'vehicle.isOrder' => ['$eq' => true],
            [
                '$group' => [
                    '_id' => 'vehicleByMop',
                    'vehicle' => ['$push' => '$vehicle'],
                ],
            ],
        ];

        $vehicle = $this->mongoService->aggregate(self::COLLECTION, $pipeline);
        $vehicle = $vehicle->getData();

        return json_decode($vehicle, true);
    }

    /**
     * update vehicle object.
     */
    public function updateVehicle(string $dbUserDataId): WSResponse
    {
        $filter = ['_id' => ['$oid' => $dbUserDataId], 'vehicle.id' => $this->vehicleModel->getId()];
        $this->vehicleModel->getVehicleOrder()->setIsUpdated(true);
       // $this->vehicleModel->setIsOrder(true);
        $fields = $this->getVehicleFieldsToUpdate();

        return $this->mongoService->updateOne(self::COLLECTION, $filter, $fields);
    }

    /**
     * get vehicle fields to update with Mongo syntaxe.
     */
    public function getVehicleFieldsToUpdate(): array
    {
        $vehicle = $this->getVehicleArray();
        $exlus = ['id'];
        $fields = [];
        foreach ($vehicle as $key => $value) {
            if (in_array($key, $exlus)) {
                continue;
            }
            if ('vehicleOrder' === $key) {
                foreach ($vehicle['vehicleOrder'] as $keyOrder => $valueOrder) {
                    $fields["vehicle.$.vehicleOrder.$keyOrder"] = $valueOrder;
                }
                continue;
            }
            $fields["vehicle.$.$key"] = $value;
        }

        return $fields;
    }

    /**
     * insert new vehicle object to DB.
     */
    public function insertVehicle(string $dbUserDataId): WSResponse
    {
        $this->vehicleModel->setId($this->getNewUuid());
       // $this->vehicleModel->setIsOrder(true);
        $this->vehicleModel->getVehicleOrder()->setIsUpdated(true);
        $filter = ['_id' => ['$oid' => $dbUserDataId]];
        $fields = ['vehicle' => $this->getVehicleArray()];
        return $this->mongoService->updatePush(self::COLLECTION, $filter, $fields);
    }

    /**
     * get user data infos from DB.
     */
    public function getUserData(string $userId): string
    {
        $userData = $this->mongoService->find(self::COLLECTION, ['userId' => $userId]);
        $userData = json_decode($userData->getData(), true);
        return $userData['documents'][0]['_id'] ?? '';
    }

    /**
     * generate new Uuid as like new ID.
     */
    public function getNewUuid(): string
    {
        return (string) Uuid::v4();
    }

    /**
     * get vehicle object like array to insert/update.
     */
    public function getVehicleArray(): array
    {
        $vehicle = json_decode($this->serializer->serialize($this->vehicleModel, 'json'), true);

        return $vehicle;
    }

    /**
     * Get all vehicles summary.
     */
    public function getOrderSummary(string $orderFormId, string $mopId, string $brand, string $country): WSResponse
    {
        try {
            $this->logger->info(__METHOD__ . " for mopId $brand/$country/$mopId");

            return $this->sysOmniConnector->call(Request::METHOD_GET, "/v1/orders/{$orderFormId}/summary", [
                'query' => [
                    'mop' => $mopId,
                    'type' => 'ORD',
                ],
                'headers' => [
                    'brand' => $brand,
                    'country' => $country,
                ],
            ]);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Catched Exception '.$e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * get vehicle data with GB cases.
     */
    public function getVehicleData(array $vehicle): array
    {
        if ($this->isOVVehicle($vehicle['versionId'])) {
            $allAttributes = $this->getVinAttributes($vehicle['vin'], $vehicle['brand']);
            $brand = $vehicle['brand'] = $this->getVehicleBrand($allAttributes, $vehicle['country']);
            if ('VX' == $brand) {
                $vehicle['language'] = 'en';
                $vehicle['country'] = 'GB';
            }
        }

        return $vehicle;
    }

    /**
     * check if its OP or VX vehicle for GB case.
     */
    public function isOVVehicle(string $versionId): bool
    {
        return 'G' == strtoupper(substr($versionId, 1, 1));
    }

    /**
     * load vehicle from corvet api.
     */
    public function getVinAttributes(string $vin, string $brand): array
    {
        $vehicleData = $this->corvetService->getData($vin, $brand);

        return $vehicleData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
    }

    /**
     * get vehicle from corvet api.
     */
    public function getDataFromCorvet(string $vin, string $brand): array
    {
        return $this->corvetService->getData($vin, $brand);
        /*
        $response = $this->sysCorvetDataConnector->call(Request::METHOD_GET, "/v1/corvet/{$vin}/data", [
            'query' => ['brand' => $brand],
        ]);

        return $response->getData()['success'];
        */
    }

    /**
     * check if its OP or VX vehicle.
     */
    public function getVehicleBrand(?array $attributes = [], ?string $country = null): string
    {
        foreach ($attributes as $attribute) {
            if ('DZZ' == substr($attribute, 0, 3)) {
                $rest = substr($attribute, 3, 2);
                if ('0V' === $rest) {
                    return 'VX';
                } elseif ('01' === $rest) {
                    return 'OP';
                }
            }
        }

        return (isset($country) && 'GB' === $country) ? 'VX' : 'OP';
    }

    public function updateVehicleIsOrderFlag(string $userId, string $vehicleId, bool $isOrder): WSResponse
    {
        $filter = ['userId' => $userId, 'vehicle.id' => $vehicleId];
        $fields = ['vehicle.$.isOrder' => $isOrder];

        return $this->mongoService->updateOne(self::COLLECTION, $filter, $fields);
    }
}
