{"doctrine/annotations": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}}, "enqueue/rdkafka": {"version": "0.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.8", "ref": "52a1ce303070ba433874414402e240cca13ed17d"}}, "friendsofphp/php-cs-fixer": {"version": "3.17", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "nelmio/api-doc-bundle": {"version": "4.11", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}}, "phpstan/phpstan": {"version": "1.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/console": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["./bin/console"]}, "symfony/flex": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": ["./.env"]}, "symfony/framework-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "a91c965766ad3ff2ae15981801643330eb42b6a5"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/maker-bundle": {"version": "1.48", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "213676c4ec929f046dfde5ea8e97625b81bc0578"}, "files": ["config/packages/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "1f5830c331065b6e4c9d5fa2105e322d29fcd573"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "e0a11b4ccb8c9e70b574ff5ad3dfdcd41dec5aa6"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/twig-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "b7772eb20e92f3fb4d4fe756e7505b4ba2ca1a2c"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "d294ad4add3e15d7eb1bae0221588ca89b38e558"}, "files": ["config/packages/uid.yaml"]}, "symfony/validator": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "twig/extra-bundle": {"version": "v3.5.1"}}