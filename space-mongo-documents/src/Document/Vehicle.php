<?php

namespace Space\MongoDocuments\Document;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class Vehicle
{
    // Fields ordered to match MongoDB document structure

    // Primary identifier - VIN always first
    #[MongoDB\Field(type: 'string')]
    private ?string $vin = null;

    // Core vehicle identification
    #[MongoDB\Field(type: 'string')]
    private ?string $brand = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $model = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $version = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $versionId = null;

    // Registration information
    #[MongoDB\Field(type: 'string')]
    private ?string $registrationNumber = null;

    #[MongoDB\Field(type: 'date')]
    private ?\DateTime $registrationDate = null;

    // Vehicle characteristics
    #[MongoDB\Field(type: 'string')]
    private ?string $color = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $energy = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $status = null;

    // Feature codes (backward compatibility array) - matches MongoDB order
    #[MongoDB\Field(type: 'collection')]
    private array $featureCode = [];

    // Preferred dealer information
    #[MongoDB\Field(type: 'hash')]
    private ?array $preferredDealer = null;

    // Enhanced embedded documents
    #[MongoDB\EmbedMany(targetDocument: FeatureCode::class)]
    private Collection $featureCodes;

    #[MongoDB\EmbedOne(targetDocument: MileageData::class)]
    private ?MileageData $mileage = null;

    public function __construct()
    {
        $this->featureCodes = new ArrayCollection();
    }

    // Primary identifier methods - VIN first
    public function getVin(): ?string
    {
        return $this->vin;
    }

    public function setVin(?string $vin): self
    {
        $this->vin = $vin;
        return $this;
    }

    // Core vehicle identification methods (matching MongoDB order)
    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }

    public function getModel(): ?string
    {
        return $this->model;
    }

    public function setModel(?string $model): self
    {
        $this->model = $model;
        return $this;
    }

    public function getVersion(): ?string
    {
        return $this->version;
    }

    public function setVersion(?string $version): self
    {
        $this->version = $version;
        return $this;
    }

    public function getVersionId(): ?string
    {
        return $this->versionId;
    }

    public function setVersionId(?string $versionId): self
    {
        $this->versionId = $versionId;
        return $this;
    }

    // Registration information methods
    public function getRegistrationNumber(): ?string
    {
        return $this->registrationNumber;
    }

    public function setRegistrationNumber(?string $registrationNumber): self
    {
        $this->registrationNumber = $registrationNumber;
        return $this;
    }

    public function getRegistrationDate(): ?\DateTime
    {
        return $this->registrationDate;
    }

    public function setRegistrationDate(?\DateTime $registrationDate): self
    {
        $this->registrationDate = $registrationDate;
        return $this;
    }

    // Vehicle characteristics methods
    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): self
    {
        $this->color = $color;
        return $this;
    }

    public function getEnergy(): ?string
    {
        return $this->energy;
    }

    public function setEnergy(?string $energy): self
    {
        $this->energy = $energy;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getFeatureCode(): array
    {
        return $this->featureCode;
    }

    public function setFeatureCode(array $featureCode): self
    {
        $this->featureCode = $featureCode;
        return $this;
    }

    // Preferred dealer methods
    public function getPreferredDealer(): ?array
    {
        return $this->preferredDealer;
    }

    public function setPreferredDealer(?array $preferredDealer): self
    {
        $this->preferredDealer = $preferredDealer;
        return $this;
    }

    /**
     * Find a specific feature code by code name
     */
    public function findFeatureCode(string $code): ?array
    {
        foreach ($this->featureCode as $featureCodeItem) {
            if (isset($featureCodeItem['code']) && $featureCodeItem['code'] === $code) {
                return $featureCodeItem;
            }
        }
        return null;
    }

    /**
     * Add a feature code to the vehicle
     */
    public function addFeatureCode(array $featureCodeItem): self
    {
        $this->featureCode[] = $featureCodeItem;
        return $this;
    }

    // Enhanced FeatureCode methods using embedded documents

    /**
     * Get feature codes as FeatureCode objects
     */
    public function getFeatureCodes(): Collection
    {
        return $this->featureCodes;
    }

    /**
     * Set feature codes from FeatureCode objects
     */
    public function setFeatureCodes(Collection $featureCodes): self
    {
        $this->featureCodes = $featureCodes;
        return $this;
    }

    /**
     * Add a FeatureCode object
     */
    public function addFeatureCodeObject(FeatureCode $featureCode): self
    {
        $this->featureCodes->add($featureCode);
        return $this;
    }

    /**
     * Remove a FeatureCode object
     */
    public function removeFeatureCodeObject(FeatureCode $featureCode): self
    {
        $this->featureCodes->removeElement($featureCode);
        return $this;
    }

    /**
     * Find a specific feature code by code name (enhanced version)
     */
    public function findFeatureCodeObject(string $code): ?FeatureCode
    {
        foreach ($this->featureCodes as $featureCode) {
            if ($featureCode->getCode() === $code) {
                return $featureCode;
            }
        }
        return null;
    }

    /**
     * Check if a feature code is enabled
     */
    public function isFeatureCodeEnabled(string $code): bool
    {
        $featureCode = $this->findFeatureCodeObject($code);
        return $featureCode && $featureCode->isEnabled();
    }

    /**
     * Get all enabled feature codes
     */
    public function getEnabledFeatureCodes(): array
    {
        $enabled = [];
        foreach ($this->featureCodes as $featureCode) {
            if ($featureCode->isEnabled()) {
                $enabled[] = $featureCode;
            }
        }
        return $enabled;
    }

    // Mileage methods

    /**
     * Get mileage data
     */
    public function getMileage(): ?MileageData
    {
        return $this->mileage;
    }

    /**
     * Set mileage data
     */
    public function setMileage(?MileageData $mileage): self
    {
        $this->mileage = $mileage;
        return $this;
    }

    /**
     * Set mileage from array (backward compatibility)
     */
    public function setMileageFromArray(array $mileageData): self
    {
        $this->mileage = MileageData::fromArray($mileageData);
        return $this;
    }

    /**
     * Get mileage as array (backward compatibility)
     */
    public function getMileageAsArray(): ?array
    {
        return $this->mileage?->toArray();
    }

    // Backward compatibility and synchronization methods

    /**
     * Sync feature codes from array to embedded documents
     */
    public function syncFeatureCodesFromArray(): self
    {
        $this->featureCodes->clear();

        foreach ($this->featureCode as $featureCodeData) {
            if (is_array($featureCodeData)) {
                $this->featureCodes->add(FeatureCode::fromArray($featureCodeData));
            }
        }

        return $this;
    }

    /**
     * Sync feature codes from embedded documents to array
     */
    public function syncFeatureCodesToArray(): self
    {
        $this->featureCode = [];

        foreach ($this->featureCodes as $featureCode) {
            $this->featureCode[] = $featureCode->toArray();
        }

        return $this;
    }

    /**
     * Set feature codes from array and sync to embedded documents
     */
    public function setFeatureCodeWithSync(array $featureCode): self
    {
        $this->featureCode = $featureCode;
        $this->syncFeatureCodesFromArray();
        return $this;
    }

    /**
     * Get feature codes as array with sync from embedded documents
     */
    public function getFeatureCodeWithSync(): array
    {
        $this->syncFeatureCodesToArray();
        return $this->featureCode;
    }

    /**
     * Auto-sync method to be called before persistence
     */
    public function prePersist(): void
    {
        // Sync embedded documents to arrays for backward compatibility
        $this->syncFeatureCodesToArray();
    }

    /**
     * Auto-sync method to be called after loading from database
     */
    public function postLoad(): void
    {
        // Sync arrays to embedded documents for enhanced functionality
        $this->syncFeatureCodesFromArray();
    }
}
